# LLM Model Selection Implementation

## Overview

Implemented a comprehensive LLM model selection system that fetches available models from the `/api/chat/models` endpoint and allows users to select their preferred model for chat conversations.

## API Integration

### Models API Endpoint
- **URL**: `GET /api/chat/models`
- **Response Format**:
```json
{
  "success": true,
  "message": "Available models retrieved successfully",
  "data": {
    "models": [
      "gpt-3.5-turbo",
      "gpt-4",
      "gpt-4-turbo-preview",
      "claude-3-haiku-20240307",
      "claude-3-sonnet-20240229",
      "claude-3-opus-20240229"
    ],
    "stats": {
      "totalModels": 6,
      "availableModels": [...],
      "openAIModels": [...],
      "anthropicModels": [...]
    }
  }
}
```

### Message API Integration
- **URL**: `POST /api/threads/message`
- **Enhanced Payload**: Now includes `llmModel` parameter
```json
{
  "message": "User message text",
  "threadId": "optional-thread-id",
  "projectId": "optional-project-id",
  "llmModel": "selected-model-id"
}
```

## Implementation Details

### 1. Model Service (`src/services/modelService.js`)
- **Purpose**: Handles model-related API calls and utilities
- **Key Features**:
  - Fetches available models from API
  - Formats model names for display
  - Provides model metadata (provider, tier, description, icons)
  - Error handling with fallback models

### 2. Enhanced ChatContext (`src/contexts/ChatContext.js`)
- **New State**:
  - `availableModels`: Array of available model IDs
  - `selectedModel`: Currently selected model (default: 'gpt-3.5-turbo')
- **New Actions**:
  - `SET_AVAILABLE_MODELS`: Updates available models list
  - `SET_SELECTED_MODEL`: Updates selected model
- **New Functions**:
  - `loadAvailableModels()`: Fetches models from API
  - `setSelectedModel(modelId)`: Updates selected model
- **Enhanced sendMessage**: Automatically includes selected model in API calls

### 3. ModelSelector Component (`src/components/chat/ModelSelector/`)
- **Features**:
  - Dropdown interface for model selection
  - Visual model information (name, provider, tier, description)
  - Icons for different providers (OpenAI 🤖, Anthropic 🧠)
  - Tier indicators (basic, advanced, premium)
  - Responsive design with mobile support
  - Accessibility features (keyboard navigation, ARIA labels)

### 4. Enhanced ChatInput (`src/components/chat/ChatInput/ChatInput.js`)
- **Integration**: Replaced model button with ModelSelector component
- **Positioning**: Model selector positioned on the right side of actions bar
- **State Management**: Disabled when chat input is disabled/loading

## File Structure

```
src/
├── services/
│   └── modelService.js                 # Model API service
├── contexts/
│   └── ChatContext.js                  # Enhanced with model state
├── components/
│   └── chat/
│       ├── ModelSelector/
│       │   ├── ModelSelector.js        # Model selection component
│       │   └── ModelSelector.css       # Styling
│       └── ChatInput/
│           ├── ChatInput.js            # Enhanced with model selector
│           └── ChatInput.css           # Updated styling
```

## Model Information System

### Model Display Names
- `gpt-3.5-turbo` → "GPT-3.5 Turbo"
- `gpt-4` → "GPT-4"
- `gpt-4-turbo-preview` → "GPT-4 Turbo"
- `claude-3-haiku-20240307` → "Claude 3 Haiku"
- `claude-3-sonnet-20240229` → "Claude 3 Sonnet"
- `claude-3-opus-20240229` → "Claude 3 Opus"

### Provider Detection
- **OpenAI**: Models starting with "gpt-"
- **Anthropic**: Models starting with "claude-"

### Tier Classification
- **Basic**: gpt-3.5-turbo, claude-3-haiku
- **Advanced**: gpt-4, claude-3-sonnet
- **Premium**: gpt-4-turbo-preview, claude-3-opus

## User Experience

### Model Selection Flow
1. **Initial Load**: Default model (gpt-3.5-turbo) is selected
2. **Model Loading**: Available models fetched when user authenticates
3. **Selection Interface**: Click model selector to see dropdown
4. **Model Information**: Each model shows name, provider, tier, and description
5. **Selection Feedback**: Selected model highlighted with checkmark
6. **Message Sending**: Selected model automatically included in API calls

### Visual Design
- **Compact Trigger**: Shows current model with icon and provider
- **Rich Dropdown**: Detailed model information with visual hierarchy
- **Responsive**: Adapts to mobile screens
- **Consistent**: Matches application design system

## Error Handling

### API Failures
- **Graceful Degradation**: Falls back to default models if API fails
- **User Feedback**: Console warnings for debugging
- **Retry Logic**: Automatic retry on model loading failure

### Model Validation
- **Default Selection**: Always has a valid model selected
- **Fallback Models**: Provides basic models if API unavailable
- **State Consistency**: Model state synchronized across components

## Testing

### Manual Testing Steps
1. **Load Application**: Verify default model is selected
2. **Open Model Selector**: Check dropdown shows available models
3. **Select Different Model**: Verify selection updates UI
4. **Send Message**: Confirm selected model included in API call
5. **Network Failure**: Test graceful degradation when API fails

### API Testing
```javascript
// Test model selection in browser console
const { setSelectedModel } = useChat();
setSelectedModel('gpt-4');

// Verify in network tab that subsequent messages include:
// { "llmModel": "gpt-4", ... }
```

## Future Enhancements

### Potential Improvements
1. **Model Capabilities**: Show token limits, pricing, speed indicators
2. **Usage Statistics**: Track model usage and preferences
3. **Smart Suggestions**: Recommend models based on conversation type
4. **Model Comparison**: Side-by-side model feature comparison
5. **Custom Models**: Support for user-added custom models
6. **Model Presets**: Save model preferences for different use cases

### Performance Optimizations
1. **Model Caching**: Cache model list in localStorage
2. **Lazy Loading**: Load model details on demand
3. **Preloading**: Prefetch model information
4. **Debounced Selection**: Prevent rapid model switching

## Integration Points

### With Existing Features
- **Thread Management**: Model selection persists across threads
- **Project Chats**: Model selection applies to project conversations
- **Message History**: Previous messages show which model was used
- **User Preferences**: Model selection could be saved to user profile

### API Compatibility
- **Backward Compatible**: Works with existing message API
- **Optional Parameter**: `llmModel` parameter is optional
- **Server Validation**: Server validates model availability
- **Error Responses**: Handles invalid model selections gracefully

This implementation provides a robust, user-friendly model selection system that enhances the chat experience while maintaining compatibility with existing functionality.
