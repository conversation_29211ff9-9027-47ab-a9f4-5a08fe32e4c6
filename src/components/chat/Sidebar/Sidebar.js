import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import { useChat } from '../../../contexts/ChatContext';
import ProjectModal from '../ProjectModal/ProjectModal';
import './Sidebar.css';

const Sidebar = ({ isOpen, onToggle }) => {
  const [activeItem, setActiveItem] = useState('new-chat');
  const [showProjectModal, setShowProjectModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const { user, logout, isAuthenticated } = useAuth();
  const {
    threads,
    projects,
    currentThread,
    currentProject,
    isLoading,
    startNewChat,
    selectThread,
    selectProject,
    searchProjects,
    searchResults,
    loadThreads,
    loadProjects
  } = useChat();
  const navigate = useNavigate();

  const menuItems = [
    { id: 'new-chat', label: 'New chat', icon: '💬' },
    { id: 'projects', label: 'Projects', icon: '📁' },
    { id: 'search', label: 'Search', icon: '🔍' }
  ];

  const bottomItems = [
    { id: 'settings', label: 'Settings', icon: '⚙️' }
  ];

  // Note: Data loading is handled by ChatContext, no need to duplicate here

  const handleItemClick = (itemId) => {
    setActiveItem(itemId);

    if (itemId === 'new-chat') {
      startNewChat();
    } else if (itemId === 'search') {
      setShowSearch(!showSearch);
    } else if (itemId === 'settings') {
      navigate('/profile');
    }
  };

  const handleNewChat = () => {
    startNewChat();
    setActiveItem('new-chat');
  };

  const handleCreateProject = () => {
    setShowProjectModal(true);
  };

  const handleThreadClick = (thread) => {
    selectThread(thread);
    setActiveItem('threads');
  };

  const handleProjectClick = (project) => {
    selectProject(project);
    setActiveItem('projects');
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      searchProjects(searchQuery.trim());
    }
  };

  const handleSearchInputChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const getUserInitials = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
    } else if (user?.email) {
      return user.email[0].toUpperCase();
    }
    return 'G';
  };

  const getUserName = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName} ${user.lastName}`;
    } else if (user?.firstName) {
      return user.firstName;
    } else if (user?.email) {
      return user.email.split('@')[0];
    }
    return 'Guest';
  };

  const getUserPlan = () => {
    return user?.plan || 'Free Plan';
  };

  return (
    <div className={`sidebar ${isOpen ? 'sidebar--open' : 'sidebar--closed'}`}>
      {/* Header */}
      <div className="sidebar__header">
        <div className="sidebar__logo">
          <h2>TheInfini AI</h2>
        </div>
        <button className="sidebar__toggle" onClick={onToggle}>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
      </div>

      {/* New Chat Button */}
      <div className="sidebar__new-chat">
        <button className="sidebar__new-chat-btn" onClick={handleNewChat}>
          <span className="sidebar__new-chat-icon">+</span>
          {isOpen && <span>New chat</span>}
        </button>
      </div>

      {/* Search Section */}
      {showSearch && isOpen && (
        <div className="sidebar__search">
          <form onSubmit={handleSearch} className="sidebar__search-form">
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchInputChange}
              placeholder="Search projects..."
              className="sidebar__search-input"
            />
            <button type="submit" className="sidebar__search-btn">
              🔍
            </button>
          </form>

          {searchResults.length > 0 && (
            <div className="sidebar__search-results">
              <div className="sidebar__section-title">Search Results</div>
              {searchResults.map(project => (
                <div
                  key={project.id}
                  className={`sidebar__item ${currentProject?.id === project.id ? 'sidebar__item--active' : ''}`}
                  onClick={() => handleProjectClick(project)}
                >
                  <span className="sidebar__item-icon">📁</span>
                  <span className="sidebar__item-text">{project.name}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Projects Section */}
      {isOpen && (
        <div className="sidebar__section">
          <div className="sidebar__section-header">
            <span className="sidebar__section-title">Projects</span>
            <button
              className="sidebar__section-action"
              onClick={handleCreateProject}
              title="Create new project"
            >
              +
            </button>
          </div>

          {isLoading ? (
            <div className="sidebar__loading">Loading...</div>
          ) : (
            <div className="sidebar__items">
              {projects.map(project => (
                <div
                  key={project.id}
                  className={`sidebar__item ${currentProject?.id === project.id ? 'sidebar__item--active' : ''}`}
                  onClick={() => handleProjectClick(project)}
                >
                  <span className="sidebar__item-icon">📁</span>
                  <span className="sidebar__item-text">{project.name}</span>
                </div>
              ))}
              {projects.length === 0 && (
                <div className="sidebar__empty">No projects yet</div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Threads Section */}
      {isOpen && (
        <div className="sidebar__section">
          <div className="sidebar__section-header">
            <span className="sidebar__section-title">Recent Chats</span>
          </div>

          <div className="sidebar__items">
            {threads.map(thread => (
              <div
                key={thread.id}
                className={`sidebar__item ${currentThread?.id === thread.id ? 'sidebar__item--active' : ''}`}
                onClick={() => handleThreadClick(thread)}
              >
                <span className="sidebar__item-icon">💬</span>
                <span className="sidebar__item-text">{thread.name}</span>
              </div>
            ))}
            {threads.length === 0 && (
              <div className="sidebar__empty">No chats yet</div>
            )}
          </div>
        </div>
      )}

      {/* Main Menu Items */}
      <nav className="sidebar__nav">
        <ul className="sidebar__nav-list">
          {menuItems.map((item) => (
            <li key={item.id}>
              <button
                className={`sidebar__nav-item ${activeItem === item.id ? 'sidebar__nav-item--active' : ''}`}
                onClick={() => handleItemClick(item.id)}
              >
                <span className="sidebar__nav-icon">{item.icon}</span>
                {isOpen && <span className="sidebar__nav-label">{item.label}</span>}
              </button>
            </li>
          ))}
        </ul>
      </nav>

      {/* Bottom Section */}
      <div className="sidebar__bottom">
        {/* Settings */}
        {bottomItems.map((item) => (
          <button
            key={item.id}
            className={`sidebar__nav-item ${activeItem === item.id ? 'sidebar__nav-item--active' : ''}`}
            onClick={() => handleItemClick(item.id)}
          >
            <span className="sidebar__nav-icon">{item.icon}</span>
            {isOpen && <span className="sidebar__nav-label">{item.label}</span>}
          </button>
        ))}

        {/* User Profile */}
        <div className="sidebar__user-profile" onClick={() => navigate('/profile')}>
          <div className="sidebar__user-avatar">
            {user?.profilePicture ? (
              <img
                src={user.profilePicture}
                alt="Profile"
                className="sidebar__user-avatar-image"
              />
            ) : (
              <div className="sidebar__user-avatar-placeholder">
                {getUserInitials()}
              </div>
            )}
          </div>
          {isOpen && (
            <div className="sidebar__user-info">
              <div className="sidebar__user-name">{getUserName()}</div>
              <div className="sidebar__user-plan">{getUserPlan()}</div>
            </div>
          )}
          {isOpen && (
            <button
              className="sidebar__logout-btn"
              onClick={(e) => {
                e.stopPropagation();
                handleLogout();
              }}
              title="Logout"
            >
              🚪
            </button>
          )}
        </div>
      </div>

      {/* Project Modal */}
      <ProjectModal
        isOpen={showProjectModal}
        onClose={() => setShowProjectModal(false)}
      />
    </div>
  );
};

export default Sidebar;
