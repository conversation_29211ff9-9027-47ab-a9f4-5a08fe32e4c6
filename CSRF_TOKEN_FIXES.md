# CSRF Token Issue Fixes

## Problem Identified

The `/projects` API endpoint was returning:
```json
{
    "success": false,
    "message": "Invalid CSRF token",
    "timestamp": "2025-05-29T10:21:56.035Z"
}
```

## Root Cause Analysis

1. **Incomplete CSRF Token Coverage**: The original implementation only added CSRF tokens to POST, PUT, and DELETE requests
2. **Server Requirement**: The server requires CSRF tokens for ALL authenticated requests, including GET requests
3. **Token Invalidation**: No proper handling when CSRF tokens become invalid
4. **Missing Retry Logic**: No automatic retry mechanism when CSRF token errors occur

## Fixes Applied

### 1. Extended CSRF Token Coverage (src/services/apiClient.js)

**Before**: Only POST, PUT, DELETE requests got CSRF tokens
```javascript
if (['post', 'put', 'delete'].includes(config.method?.toLowerCase())) {
  // Add CSRF token logic
}
```

**After**: All authenticated requests get CSRF tokens
```javascript
if (token) { // If user is authenticated
  // Add CSRF token logic for ALL requests
}
```

### 2. Added CSRF Token Retry Logic

**New Feature**: Automatic retry when CSRF token is invalid
```javascript
else if (error.response?.status === 403 && 
         error.response?.data?.message?.includes('CSRF') && 
         !originalRequest._retry) {
  // Clear invalid token and retry once
  originalRequest._retry = true;
  localStorage.removeItem('csrfToken');
  csrfTokenPromise = null;
  return await apiClient(originalRequest);
}
```

### 3. Added Manual CSRF Token Refresh Function

**New Export**: `refreshCSRFToken()` function for manual token refresh
```javascript
export const refreshCSRFToken = async () => {
  localStorage.removeItem('csrfToken');
  csrfTokenPromise = null;
  // Fetch new token...
};
```

### 4. Created CSRF Debugging Tools

**New File**: `src/utils/csrfDebugger.js`
- Token status checking
- Endpoint testing
- Comprehensive debug information
- Manual token refresh and testing

**Enhanced**: API Call Debugger with CSRF testing buttons
- Test CSRF token functionality
- Test specific endpoints (like `/projects`)
- View CSRF debug information

## Files Modified

1. **src/services/apiClient.js**
   - Extended CSRF token coverage to all authenticated requests
   - Added automatic retry logic for invalid CSRF tokens
   - Added manual refresh function

2. **src/components/debug/ApiCallDebugger.js**
   - Added CSRF testing buttons
   - Integrated CSRF debugger functionality

3. **src/components/debug/ApiCallDebugger.css**
   - Styled new CSRF testing buttons

## New Files Created

1. **src/utils/csrfDebugger.js** - Comprehensive CSRF debugging utility

## How to Test the Fixes

### 1. Using the Debug Tools (Development Mode)

1. **Open the application** in development mode
2. **Look for the API Call Debugger** in the bottom-right corner
3. **Click the debugger** to expand it
4. **Use the CSRF testing buttons**:
   - **"Test CSRF"** - Refreshes and tests CSRF token
   - **"Test /projects"** - Tests the specific problematic endpoint
   - **"CSRF Info"** - Shows comprehensive CSRF debug information

### 2. Manual Testing in Browser Console

The CSRF debugger is exposed to `window.csrfDebugger` in development:

```javascript
// Check current token status
csrfDebugger.checkTokenStatus()

// Test CSRF token
await csrfDebugger.testCSRFToken()

// Refresh and test
await csrfDebugger.refreshAndTest()

// Test specific endpoint
await csrfDebugger.testEndpoint('GET', '/projects')

// Get comprehensive debug info
csrfDebugger.getDebugInfo()

// Log debug info to console
csrfDebugger.logDebugInfo()
```

### 3. Network Tab Verification

1. **Open browser dev tools** → Network tab
2. **Navigate to a page that loads projects** (like /chat)
3. **Look for the `/projects` request**
4. **Check the request headers** - should include `X-CSRF-Token`
5. **Verify the response** - should be successful (200 status)

## Expected Results

- ✅ `/projects` endpoint should return successful responses
- ✅ All authenticated requests should include CSRF tokens
- ✅ Invalid CSRF tokens should be automatically refreshed and retried
- ✅ No more "Invalid CSRF token" errors
- ✅ Comprehensive debugging tools available in development

## Troubleshooting

If you still see CSRF token errors:

1. **Clear browser storage**: `localStorage.clear()`
2. **Use the debug tools**: Click "Test CSRF" in the API debugger
3. **Check console logs**: Look for CSRF-related warnings/errors
4. **Verify authentication**: Ensure you're properly logged in
5. **Check server logs**: Verify server-side CSRF token validation

## Production Considerations

- **Debug tools are disabled** in production builds
- **CSRF token caching** prevents unnecessary server requests
- **Automatic retry logic** handles token invalidation gracefully
- **Error handling** provides fallback behavior for CSRF failures

The fixes ensure robust CSRF token handling while providing excellent debugging capabilities during development.
